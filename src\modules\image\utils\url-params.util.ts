import { SelectedNodeList } from "../interface/image.interface";

/**
 * URL parameter keys for filter state persistence
 */
export const URL_PARAM_KEYS = {
  // Existing parameters (preserved)
  DRILLHOLE_NAMES: "name",
  VIEW_MODE: "view",
  DEPTH: "depth",

  // New filter parameters
  IMAGE_CATEGORY: "imageCategory",
  IMAGE_TYPES: "imageTypes", // Serialized hierarchical nodes
  GEOLOGY_SUITE: "geologySuite",
  GEOLOGY_FIELD: "geologyField",
  GEOPHYSICS_SUITE: "geophysicsSuite",
  GEOPHYSICS_ATTRIBUTES: "geophysicsAttrs",
  SHOW_LOGS: "showLogs",
  TEXT_WIDTH: "textWidth",
} as const;

/**
 * Interface for all URL-persistable filter states
 */
export interface FilterUrlParams {
  // Existing parameters
  drillholeNames?: string[];
  viewMode?: string;
  depth?: number;

  // New filter parameters
  imageCategory?: number;
  imageTypes?: string; // JSON string of hierarchical nodes
  geologySuite?: string; // JSON string of suite object
  geologyField?: number;
  geophysicsSuite?: string | number;
  geophysicsAttributes?: string[];
  showLogs?: boolean;
  textWidth?: boolean;
}

/**
 * Serialize hierarchical nodes to URL-safe string
 */
export function serializeHierarchicalNodes(nodes: SelectedNodeList): string {
  try {
    return encodeURIComponent(JSON.stringify(nodes));
  } catch (error) {
    console.warn("Failed to serialize hierarchical nodes:", error);
    return "";
  }
}

/**
 * Deserialize hierarchical nodes from URL string
 */
export function deserializeHierarchicalNodes(serialized: string): SelectedNodeList {
  try {
    if (!serialized) return { selectNodes: [] };
    const decoded = decodeURIComponent(serialized);
    const parsed = JSON.parse(decoded);
    return parsed && typeof parsed === "object" && Array.isArray(parsed.selectNodes)
      ? parsed
      : { selectNodes: [] };
  } catch (error) {
    console.warn("Failed to deserialize hierarchical nodes:", error);
    return { selectNodes: [] };
  }
}

/**
 * Serialize geology suite object to URL-safe string
 */
export function serializeGeologySuite(suite: { label: string; value: string | number } | null): string {
  try {
    if (!suite) return "";
    return encodeURIComponent(JSON.stringify(suite));
  } catch (error) {
    console.warn("Failed to serialize geology suite:", error);
    return "";
  }
}

/**
 * Deserialize geology suite from URL string
 */
export function deserializeGeologySuite(serialized: string): { label: string; value: string | number } | null {
  try {
    if (!serialized) return null;
    const decoded = decodeURIComponent(serialized);
    const parsed = JSON.parse(decoded);
    console.log('parsed: ', parsed);
    return parsed && typeof parsed === "object" && parsed.label && parsed.value !== undefined
      ? parsed
      : null;
  } catch (error) {
    console.warn("Failed to deserialize geology suite:", error);
    return null;
  }
}

/**
 * Convert Redux state to URL parameters
 */
export function stateToUrlParams(state: {
  selectedHoleNames: string[];
  drillholeViewMode?: string;
  selectedImageCategory?: number;
  selectedHierarchicalNodes: SelectedNodeList;
  selectedGeologySuite?: { label: string; value: string | number } | null;
  selectedGeologyFieldId?: number;
  selectedGeophysicsSuiteId?: string | number | null;
  selectedGeophysicsAttributes?: any[];
  showCombinedResultColumn: boolean;
  isTextWide: boolean;
}): FilterUrlParams {
  return {
    drillholeNames: state.selectedHoleNames.length > 0 ? state.selectedHoleNames : undefined,
    viewMode: state.drillholeViewMode,
    imageCategory: state.selectedImageCategory,
    imageTypes: state.selectedHierarchicalNodes.selectNodes.length > 0
      ? serializeHierarchicalNodes(state.selectedHierarchicalNodes)
      : undefined,
    geologySuite: state.selectedGeologySuite
      ? serializeGeologySuite(state.selectedGeologySuite)
      : undefined,
    geologyField: state.selectedGeologyFieldId,
    geophysicsSuite: state.selectedGeophysicsSuiteId || undefined,
    geophysicsAttributes: state.selectedGeophysicsAttributes && state.selectedGeophysicsAttributes.length > 0
      ? state.selectedGeophysicsAttributes
      : undefined,
    showLogs: state.showCombinedResultColumn,
    textWidth: state.isTextWide,
  };
}

/**
 * Extract filter parameters from URLSearchParams
 */
export function urlParamsToState(searchParams: URLSearchParams): Partial<FilterUrlParams> {
  const params: Partial<FilterUrlParams> = {};

  // Handle drillhole names (multiple values)
  const drillholeNames = searchParams.getAll(URL_PARAM_KEYS.DRILLHOLE_NAMES);
  if (drillholeNames.length > 0) {
    params.drillholeNames = drillholeNames;
  }

  // Handle single value parameters
  const viewMode = searchParams.get(URL_PARAM_KEYS.VIEW_MODE);
  if (viewMode) params.viewMode = viewMode;

  const depth = searchParams.get(URL_PARAM_KEYS.DEPTH);
  if (depth) {
    const depthNum = parseFloat(depth);
    if (!isNaN(depthNum)) params.depth = depthNum;
  }

  const imageCategory = searchParams.get(URL_PARAM_KEYS.IMAGE_CATEGORY);
  if (imageCategory) {
    const categoryNum = parseInt(imageCategory, 10);
    if (!isNaN(categoryNum)) params.imageCategory = categoryNum;
  }

  const imageTypes = searchParams.get(URL_PARAM_KEYS.IMAGE_TYPES);
  if (imageTypes) params.imageTypes = imageTypes;

  const geologySuite = searchParams.get(URL_PARAM_KEYS.GEOLOGY_SUITE);
  if (geologySuite) params.geologySuite = geologySuite;

  const geologyField = searchParams.get(URL_PARAM_KEYS.GEOLOGY_FIELD);
  if (geologyField) {
    const fieldNum = parseInt(geologyField, 10);
    if (!isNaN(fieldNum)) params.geologyField = fieldNum;
  }

  const geophysicsSuite = searchParams.get(URL_PARAM_KEYS.GEOPHYSICS_SUITE);
  if (geophysicsSuite) {
    // Try to parse as number, fallback to string
    const suiteNum = parseInt(geophysicsSuite, 10);
    params.geophysicsSuite = !isNaN(suiteNum) ? suiteNum : geophysicsSuite;
  }

  const geophysicsAttrs = searchParams.getAll(URL_PARAM_KEYS.GEOPHYSICS_ATTRIBUTES);
  if (geophysicsAttrs.length > 0) {
    params.geophysicsAttributes = geophysicsAttrs;
  }

  const showLogs = searchParams.get(URL_PARAM_KEYS.SHOW_LOGS);
  if (showLogs !== null) {
    params.showLogs = showLogs === "true";
  }

  const textWidth = searchParams.get(URL_PARAM_KEYS.TEXT_WIDTH);
  if (textWidth !== null) {
    params.textWidth = textWidth === "true";
  }

  return params;
}

/**
 * Update URLSearchParams with filter state while preserving other parameters
 */
export function updateUrlParams(
  currentParams: URLSearchParams,
  filterParams: FilterUrlParams
): URLSearchParams {
  const newParams = new URLSearchParams(currentParams);

  // Handle drillhole names (multiple values)
  newParams.delete(URL_PARAM_KEYS.DRILLHOLE_NAMES);
  if (filterParams.drillholeNames && filterParams.drillholeNames.length > 0) {
    filterParams.drillholeNames.forEach(name => {
      newParams.append(URL_PARAM_KEYS.DRILLHOLE_NAMES, name);
    });
  }

  // Handle single value parameters
  const singleValueParams = [
    { key: URL_PARAM_KEYS.VIEW_MODE, value: filterParams.viewMode },
    { key: URL_PARAM_KEYS.DEPTH, value: filterParams.depth?.toString() },
    { key: URL_PARAM_KEYS.IMAGE_CATEGORY, value: filterParams.imageCategory?.toString() },
    { key: URL_PARAM_KEYS.IMAGE_TYPES, value: filterParams.imageTypes },
    { key: URL_PARAM_KEYS.GEOLOGY_SUITE, value: filterParams.geologySuite },
    { key: URL_PARAM_KEYS.GEOLOGY_FIELD, value: filterParams.geologyField?.toString() },
    { key: URL_PARAM_KEYS.GEOPHYSICS_SUITE, value: filterParams.geophysicsSuite?.toString() },
    { key: URL_PARAM_KEYS.SHOW_LOGS, value: filterParams.showLogs?.toString() },
    { key: URL_PARAM_KEYS.TEXT_WIDTH, value: filterParams.textWidth?.toString() },
  ];

  singleValueParams.forEach(({ key, value }) => {
    if (value !== undefined && value !== null && value !== "") {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
  });

  // Handle geophysics attributes (multiple values)
  newParams.delete(URL_PARAM_KEYS.GEOPHYSICS_ATTRIBUTES);
  if (filterParams.geophysicsAttributes && filterParams.geophysicsAttributes.length > 0) {
    filterParams.geophysicsAttributes.forEach(attr => {
      newParams.append(URL_PARAM_KEYS.GEOPHYSICS_ATTRIBUTES, attr);
    });
  }

  return newParams;
}
