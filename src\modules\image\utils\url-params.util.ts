import { SelectedNodeList } from "../interface/image.interface";

/**
 * URL parameter keys for filter state persistence
 */
export const URL_PARAM_KEYS = {
  // Existing parameters (preserved)
  DRILLHOLE_NAMES: "name",
  VIEW_MODE: "view",
  DEPTH: "depth",

  // New filter parameters
  IMAGE_CATEGORY: "imageCategory",
  IMAGE_TYPES: "imageTypes", // Serialized hierarchical nodes
  GEOLOGY_SUITE: "geologySuite",
  GEOLOGY_FIELD: "geologyField",
  GEOPHYSICS_SUITE: "geophysicsSuite",
  GEOPHYSICS_ATTRIBUTES: "geophysicsAttrs",
  SHOW_LOGS: "showLogs",
  TEXT_WIDTH: "textWidth",
} as const;

/**
 * Interface for all URL-persistable filter states
 */
export interface FilterUrlParams {
  // Existing parameters
  drillholeNames?: string[];
  viewMode?: string;
  depth?: number;

  // New filter parameters
  imageCategory?: number;
  imageTypes?: string; // JSON string of tree values
  geologySuite?: string; // JSON string of suite object
  geologyField?: number;
  geophysicsSuite?: string | number;
  geophysicsAttributes?: string[];
  showLogs?: boolean;
  textWidth?: boolean;
}

/**
 * Convert hierarchical nodes to tree value format (for TreeSelect component)
 */
export function hierarchicalNodesToTreeValues(nodes: SelectedNodeList): string[] {
  const treeValues: string[] = [];

  nodes.selectNodes.forEach(node => {
    if (node.subTypeId) {
      // Subtype format: "subtype-{subtypeId}-type-{typeId}"
      treeValues.push(`subtype-${node.subTypeId}-type-${node.typeId}`);
    } else {
      // Type format: "type-{typeId}"
      treeValues.push(`type-${node.typeId}`);
    }
  });

  return treeValues;
}

/**
 * Convert tree values to hierarchical nodes format
 */
export function treeValuesToHierarchicalNodes(treeValues: string[]): SelectedNodeList {
  const selectNodes: SelectedNodeList["selectNodes"] = [];

  treeValues.forEach(value => {
    if (value.startsWith("subtype-")) {
      // Parse "subtype-{subtypeId}-type-{typeId}"
      const parts = value.split("-");
      if (parts.length >= 4) {
        const subtypeId = parseInt(parts[1], 10);
        const typeId = parseInt(parts[3], 10);
        if (!isNaN(subtypeId) && !isNaN(typeId)) {
          selectNodes.push({
            id: value,
            typeId: typeId,
            subTypeId: subtypeId,
          });
        }
      }
    } else if (value.startsWith("type-")) {
      // Parse "type-{typeId}"
      const parts = value.split("-");
      if (parts.length >= 2) {
        const typeId = parseInt(parts[1], 10);
        if (!isNaN(typeId)) {
          selectNodes.push({
            id: value,
            typeId: typeId,
          });
        }
      }
    }
  });

  return { selectNodes };
}

/**
 * Convert tree values to compact URL format
 * "type-123" -> "123"
 * "subtype-456-type-123" -> "456-123"
 */
export function treeValuesToCompactFormat(treeValues: string[]): string[] {
  return treeValues.map(value => {
    if (value.startsWith("subtype-")) {
      // Parse "subtype-{subtypeId}-type-{typeId}" -> "{subtypeId}-{typeId}"
      const parts = value.split("-");
      if (parts.length >= 4) {
        const subtypeId = parts[1];
        const typeId = parts[3];
        return `${subtypeId}-${typeId}`;
      }
    } else if (value.startsWith("type-")) {
      // Parse "type-{typeId}" -> "{typeId}"
      const parts = value.split("-");
      if (parts.length >= 2) {
        return parts[1];
      }
    }
    return value; // Fallback to original value
  });
}

/**
 * Convert compact URL format back to tree values
 * "123" -> "type-123"
 * "456-123" -> "subtype-456-type-123"
 */
export function compactFormatToTreeValues(compactValues: string[]): string[] {
  return compactValues.map(value => {
    if (value.includes("-")) {
      // Format: "{subtypeId}-{typeId}" -> "subtype-{subtypeId}-type-{typeId}"
      const parts = value.split("-");
      if (parts.length === 2) {
        return `subtype-${parts[0]}-type-${parts[1]}`;
      }
    } else {
      // Format: "{typeId}" -> "type-{typeId}"
      return `type-${value}`;
    }
    return value; // Fallback to original value
  });
}

/**
 * Serialize tree values to compact URL-safe string
 */
export function serializeTreeValues(treeValues: string[]): string {
  try {
    const compactValues = treeValuesToCompactFormat(treeValues);
    return encodeURIComponent(JSON.stringify(compactValues));
  } catch (error) {
    console.warn("Failed to serialize tree values:", error);
    return "";
  }
}

/**
 * Deserialize tree values from compact URL string
 */
export function deserializeTreeValues(serialized: string): string[] {
  try {
    if (!serialized) return [];
    const decoded = decodeURIComponent(serialized);
    const parsed = JSON.parse(decoded);
    if (Array.isArray(parsed)) {
      return compactFormatToTreeValues(parsed);
    }
    return [];
  } catch (error) {
    console.warn("Failed to deserialize tree values:", error);
    return [];
  }
}

/**
 * Serialize geology suite object to URL-safe string
 */
export function serializeGeologySuite(suite: { label: string; value: string | number } | null): string {
  try {
    if (!suite) return "";
    return encodeURIComponent(JSON.stringify(suite));
  } catch (error) {
    console.warn("Failed to serialize geology suite:", error);
    return "";
  }
}

/**
 * Deserialize geology suite from URL string
 */
export function deserializeGeologySuite(serialized: string): { label: string; value: string | number } | null {
  try {
    if (!serialized) return null;
    const decoded = decodeURIComponent(serialized);
    const parsed = JSON.parse(decoded);
    console.log('parsed: ', parsed);
    return parsed && typeof parsed === "object" && parsed.label && parsed.value !== undefined
      ? parsed
      : null;
  } catch (error) {
    console.warn("Failed to deserialize geology suite:", error);
    return null;
  }
}

/**
 * Convert Redux state to URL parameters
 */
export function stateToUrlParams(state: {
  selectedHoleNames: string[];
  drillholeViewMode?: string;
  selectedImageCategory?: number;
  selectedHierarchicalNodes: SelectedNodeList;
  selectedGeologySuite?: { label: string; value: string | number } | null;
  selectedGeologyFieldId?: number;
  selectedGeophysicsSuiteId?: string | number | null;
  selectedGeophysicsAttributes?: any[];
  showCombinedResultColumn: boolean;
  isTextWide: boolean;
}): FilterUrlParams {
  return {
    drillholeNames: state.selectedHoleNames.length > 0 ? state.selectedHoleNames : undefined,
    viewMode: state.drillholeViewMode,
    imageCategory: state.selectedImageCategory,
    imageTypes: state.selectedHierarchicalNodes.selectNodes.length > 0
      ? serializeTreeValues(hierarchicalNodesToTreeValues(state.selectedHierarchicalNodes))
      : undefined,
    geologySuite: state.selectedGeologySuite
      ? serializeGeologySuite(state.selectedGeologySuite)
      : undefined,
    geologyField: state.selectedGeologyFieldId,
    geophysicsSuite: state.selectedGeophysicsSuiteId || undefined,
    geophysicsAttributes: state.selectedGeophysicsAttributes && state.selectedGeophysicsAttributes.length > 0
      ? state.selectedGeophysicsAttributes
      : undefined,
    showLogs: state.showCombinedResultColumn,
    textWidth: state.isTextWide,
  };
}

/**
 * Extract filter parameters from URLSearchParams
 */
export function urlParamsToState(searchParams: URLSearchParams): Partial<FilterUrlParams> {
  const params: Partial<FilterUrlParams> = {};

  // Handle drillhole names (multiple values)
  const drillholeNames = searchParams.getAll(URL_PARAM_KEYS.DRILLHOLE_NAMES);
  if (drillholeNames.length > 0) {
    params.drillholeNames = drillholeNames;
  }

  // Handle single value parameters
  const viewMode = searchParams.get(URL_PARAM_KEYS.VIEW_MODE);
  if (viewMode) params.viewMode = viewMode;

  const depth = searchParams.get(URL_PARAM_KEYS.DEPTH);
  if (depth) {
    const depthNum = parseFloat(depth);
    if (!isNaN(depthNum)) params.depth = depthNum;
  }

  const imageCategory = searchParams.get(URL_PARAM_KEYS.IMAGE_CATEGORY);
  if (imageCategory) {
    const categoryNum = parseInt(imageCategory, 10);
    if (!isNaN(categoryNum)) params.imageCategory = categoryNum;
  }

  const imageTypes = searchParams.get(URL_PARAM_KEYS.IMAGE_TYPES);
  if (imageTypes) params.imageTypes = imageTypes;

  const geologySuite = searchParams.get(URL_PARAM_KEYS.GEOLOGY_SUITE);
  if (geologySuite) params.geologySuite = geologySuite;

  const geologyField = searchParams.get(URL_PARAM_KEYS.GEOLOGY_FIELD);
  if (geologyField) {
    const fieldNum = parseInt(geologyField, 10);
    if (!isNaN(fieldNum)) params.geologyField = fieldNum;
  }

  const geophysicsSuite = searchParams.get(URL_PARAM_KEYS.GEOPHYSICS_SUITE);
  if (geophysicsSuite) {
    // Try to parse as number, fallback to string
    const suiteNum = parseInt(geophysicsSuite, 10);
    params.geophysicsSuite = !isNaN(suiteNum) ? suiteNum : geophysicsSuite;
  }

  const geophysicsAttrs = searchParams.getAll(URL_PARAM_KEYS.GEOPHYSICS_ATTRIBUTES);
  if (geophysicsAttrs.length > 0) {
    params.geophysicsAttributes = geophysicsAttrs;
  }

  const showLogs = searchParams.get(URL_PARAM_KEYS.SHOW_LOGS);
  if (showLogs !== null) {
    params.showLogs = showLogs === "true";
  }

  const textWidth = searchParams.get(URL_PARAM_KEYS.TEXT_WIDTH);
  if (textWidth !== null) {
    params.textWidth = textWidth === "true";
  }

  return params;
}

/**
 * Update URLSearchParams with filter state while preserving other parameters
 */
export function updateUrlParams(
  currentParams: URLSearchParams,
  filterParams: FilterUrlParams
): URLSearchParams {
  const newParams = new URLSearchParams(currentParams);

  // Handle drillhole names (multiple values)
  newParams.delete(URL_PARAM_KEYS.DRILLHOLE_NAMES);
  if (filterParams.drillholeNames && filterParams.drillholeNames.length > 0) {
    filterParams.drillholeNames.forEach(name => {
      newParams.append(URL_PARAM_KEYS.DRILLHOLE_NAMES, name);
    });
  }

  // Handle single value parameters
  const singleValueParams = [
    { key: URL_PARAM_KEYS.VIEW_MODE, value: filterParams.viewMode },
    { key: URL_PARAM_KEYS.DEPTH, value: filterParams.depth?.toString() },
    { key: URL_PARAM_KEYS.IMAGE_CATEGORY, value: filterParams.imageCategory?.toString() },
    { key: URL_PARAM_KEYS.IMAGE_TYPES, value: filterParams.imageTypes },
    { key: URL_PARAM_KEYS.GEOLOGY_SUITE, value: filterParams.geologySuite },
    { key: URL_PARAM_KEYS.GEOLOGY_FIELD, value: filterParams.geologyField?.toString() },
    { key: URL_PARAM_KEYS.GEOPHYSICS_SUITE, value: filterParams.geophysicsSuite?.toString() },
    { key: URL_PARAM_KEYS.SHOW_LOGS, value: filterParams.showLogs?.toString() },
    { key: URL_PARAM_KEYS.TEXT_WIDTH, value: filterParams.textWidth?.toString() },
  ];

  singleValueParams.forEach(({ key, value }) => {
    if (value !== undefined && value !== null && value !== "") {
      newParams.set(key, value);
    } else {
      newParams.delete(key);
    }
  });

  // Handle geophysics attributes (multiple values)
  newParams.delete(URL_PARAM_KEYS.GEOPHYSICS_ATTRIBUTES);
  if (filterParams.geophysicsAttributes && filterParams.geophysicsAttributes.length > 0) {
    filterParams.geophysicsAttributes.forEach(attr => {
      newParams.append(URL_PARAM_KEYS.GEOPHYSICS_ATTRIBUTES, attr);
    });
  }

  return newParams;
}
