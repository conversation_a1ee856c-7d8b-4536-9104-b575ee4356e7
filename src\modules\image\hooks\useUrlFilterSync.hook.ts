import { useCallback, useEffect, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAppDispatch, useAppSelector } from "@/common/vendors/redux/store/hook";
import {
  updateSelectedImageCategory,
  setSelectedHierarchicalNodes,
  setSelectedNodeValues,
  updateSelectedGeologySuite,
  updateSelectedGeologyFieldId,
  updateSelectedGeophysicsSuiteId,
  updateSelectedGeophysicsAttributes,
  updateCombinedResultColumnVisibility,
  updateTextWidth,
  updateSelectedHoleNames,
  updateDrillholeView,
} from "../redux/imageSlice";
import {
  stateToUrlParams,
  urlParamsToState,
  updateUrlParams,
  deserializeHierarchicalNodes,
  deserializeGeologySuite,
} from "../utils/url-params.util";
import { EnumDrillholeView } from "../model/enum/drillhole.enum";

/**
 * Custom hook for bidirectional URL and Redux state synchronization
 * Handles reading filter state from URL on mount and updating URL when state changes
 */
export function useUrlFilterSync(
  onDepthParamChange?: (depth: number | null) => void,
  currentDepth?: number | null
) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();

  // Get current Redux state
  const {
    selectedHoleNames,
    drillholeViewMode,
    selectedImageCategory,
    selectedHierarchicalNodes,
    selectedNodeValues,
    selectedGeologySuite,
    selectedGeologyFieldId,
    selectedGeophysicsSuiteId,
    selectedGeophysicsAttributes,
    showCombinedResultColumn,
    isTextWide,
  } = useAppSelector((state) => state.images);

  // Track if we're initializing from URL to prevent circular updates
  const isInitializing = useRef(true);
  const lastUrlParams = useRef<string>("");

  /**
   * Initialize Redux state from URL parameters on component mount
   */
  const initializeFromUrl = useCallback(() => {
    const urlParams = urlParamsToState(searchParams);
    console.log('urlParams: ', urlParams);

    // Update Redux state based on URL parameters
    if (urlParams.drillholeNames && urlParams.drillholeNames.length > 0) {
      dispatch(updateSelectedHoleNames(urlParams.drillholeNames));
    }

    if (urlParams.viewMode && Object.values(EnumDrillholeView).includes(urlParams.viewMode as EnumDrillholeView)) {
      dispatch(updateDrillholeView(urlParams.viewMode as EnumDrillholeView));
    }

    if (urlParams.imageCategory !== undefined) {
      dispatch(updateSelectedImageCategory(urlParams.imageCategory));
    }

    if (urlParams.imageTypes) {
      const hierarchicalNodes = deserializeHierarchicalNodes(urlParams.imageTypes);
      console.log('hierarchicalNodes: ', hierarchicalNodes);
      dispatch(setSelectedHierarchicalNodes(hierarchicalNodes));

      // Extract flat node values for backward compatibility
      const nodeValues = hierarchicalNodes.selectNodes.map(node =>
        node.subTypeId ? `${node.typeId}-${node.subTypeId}` : node.typeId.toString()
      );
      dispatch(setSelectedNodeValues(nodeValues));
    }

    if (urlParams.geologySuite) {
      const geologySuite = deserializeGeologySuite(urlParams.geologySuite);
      console.log('geologySuite: ', geologySuite);
      dispatch(updateSelectedGeologySuite(geologySuite));
    }

    if (urlParams.geologyField !== undefined) {
      dispatch(updateSelectedGeologyFieldId(urlParams.geologyField));
    }

    if (urlParams.geophysicsSuite !== undefined) {
      dispatch(updateSelectedGeophysicsSuiteId(urlParams.geophysicsSuite));
    }

    if (urlParams.geophysicsAttributes && urlParams.geophysicsAttributes.length > 0) {
      dispatch(updateSelectedGeophysicsAttributes(urlParams.geophysicsAttributes));
    }

    if (urlParams.showLogs !== undefined) {
      dispatch(updateCombinedResultColumnVisibility(urlParams.showLogs));
    }

    if (urlParams.textWidth !== undefined) {
      dispatch(updateTextWidth(urlParams.textWidth));
    }

    // Handle depth parameter for parent component
    if (onDepthParamChange && urlParams.depth !== undefined) {
      onDepthParamChange(urlParams.depth);
    } else if (onDepthParamChange) {
      onDepthParamChange(null);
    }

    isInitializing.current = false;
  }, [searchParams, dispatch, onDepthParamChange]);

  /**
   * Update URL when Redux state changes (after initialization)
   */
  const updateUrlFromState = useCallback(() => {
    if (isInitializing.current) return;

    const currentState = {
      selectedHoleNames,
      drillholeViewMode,
      selectedImageCategory,
      selectedHierarchicalNodes,
      selectedGeologySuite,
      selectedGeologyFieldId,
      selectedGeophysicsSuiteId,
      selectedGeophysicsAttributes,
      showCombinedResultColumn,
      isTextWide,
    };

    const filterParams = stateToUrlParams(currentState);

    // Add depth parameter if provided
    if (currentDepth !== undefined && currentDepth !== null) {
      filterParams.depth = currentDepth;
    }

    const newParams = updateUrlParams(searchParams, filterParams);
    const newParamsString = newParams.toString();

    // Only update URL if parameters actually changed
    if (newParamsString !== lastUrlParams.current) {
      lastUrlParams.current = newParamsString;
      router.replace(`${window.location.pathname}?${newParamsString}`, { scroll: false });
    }
  }, [
    router,
    searchParams,
    selectedHoleNames,
    drillholeViewMode,
    selectedImageCategory,
    selectedHierarchicalNodes,
    selectedGeologySuite,
    selectedGeologyFieldId,
    selectedGeophysicsSuiteId,
    selectedGeophysicsAttributes,
    showCombinedResultColumn,
    isTextWide,
    currentDepth,
  ]);

  /**
   * Initialize from URL on component mount
   */
  useEffect(() => {
    initializeFromUrl();
  }, []); // Only run once on mount

  /**
   * Update URL when Redux state changes (after initialization)
   */
  useEffect(() => {
    updateUrlFromState();
  }, [updateUrlFromState]);

  /**
   * Handle external URL changes (browser back/forward, direct navigation)
   */
  useEffect(() => {
    if (!isInitializing.current) {
      const currentParamsString = searchParams.toString();
      if (currentParamsString !== lastUrlParams.current) {
        lastUrlParams.current = currentParamsString;
        isInitializing.current = true; // Temporarily set to prevent circular updates
        initializeFromUrl();
      }
    }
  }, [searchParams.toString(), initializeFromUrl]);

  return {
    /**
     * Manually trigger URL update (useful for external state changes)
     */
    syncToUrl: updateUrlFromState,

    /**
     * Manually trigger state initialization from URL
     */
    syncFromUrl: initializeFromUrl,
  };
}
