"use client";
import { RequestState } from "@/common/configs/app.contants"; // Corrected RequestState import
import useEffectAfterMounted from "@/common/hooks/useSkipFirstRender";
import { SortOrder } from "@/common/interfaces/general/general.types";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { selectDetailAccountSettings } from "@/modules/account-settings/redux/accountSettingsSlice";
import drillHoleRequest from "@/modules/drillhole/api/drillhole.api";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import suiteRequest from "../../downhole-point/api/suite.api"; // Corrected path
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import geologySuiteRequest from "@/modules/geology-suite/api/geology-suite.api";
import { getDetailProject } from "@/modules/projects/redux/projectSlice/thunks";
import { getRockGroups } from "@/modules/rock-groups/redux/thunks";
import { Checkbox, Col, Row, Select, Spin, Switch, TreeSelect } from "antd";
import { isEmpty } from "lodash";
import { useSearchParams } from "next/navigation";
import React, { useEffect, useRef, useState } from "react";
import { transformToHierarchicalNodes } from "../helpers/image.helpers";
import { useGetListImage } from "../hooks/useGetListImage.hook";
import { useImageCache } from "../hooks/useImageCache";
import { ImageQuery, SelectedNodeList } from "../interface/image.interface";
import { EnumDrillholeView } from "../model/enum/drillhole.enum";
import { ImageView, imageCategoryOptions } from "../model/enum/images.enum";
import {
  clearMultiLoggings,
  selectImages, // Replaced updateSelectedImageType and updateSelectedImageSubtype
  setAllImageTypes, // Action to populate allImageTypes
  setSelectedHierarchicalNodes,
  setSelectedNodeValues,
  updateCombinedResultColumnVisibility,
  updateDrillholeView,
  updateGeologySuiteFields,
  updateGeophysicsSuiteAttributes,
  updateImagesOfDrillHoles,
  updateSelectedGeologyFieldId,
  updateSelectedGeologySuite,
  updateSelectedGeophysicsAttributes,
  updateSelectedGeophysicsSuiteId,
  updateSelectedHoleNames,
  updateSelectedImageCategory,
  updateTextWidth,
} from "../redux/imageSlice";
import { useUrlFilterSync } from "../hooks/useUrlFilterSync.hook";
import {
  selectAllImageTypes, // Add selector for allImageTypes
} from "../redux/imageSlice/selectors";

import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
// Removed useQueryImageSubType as subtypes are expected to be nested in imageTypeData

import {
  getGeophysicsData,
  getMultiGeologyData,
} from "../redux/imageSlice/thunks";
import { DrillHoleImages } from "./drillhole-view";

interface IGeophysicsSuite {
  id: number | string;
  name: string;
  // Define other properties based on actual API response
}

interface ISuiteAttribute {
  id: number | string;
  name: string;
  // Define other properties based on actual API response
}

export interface DrillHolePanelProps {
  alignValue?: ImageView;
  onDepthParamChange?: (depth: number | null) => void; // Renamed and accepts null
}

function DrillHolePanel({
  alignValue,
  onDepthParamChange, // Renamed prop
}: DrillHolePanelProps) {
  const searchParams = useSearchParams();
  const accountSettings = useAppSelector(selectDetailAccountSettings);

  // Initialize URL filter synchronization
  useUrlFilterSync(onDepthParamChange);
  const {
    selectedGeologyFieldId,
    selectedHoleNames,
    selectedGeologySuite,
    geologySuiteFields,
    selectedGeophysicsSuiteId,
    geophysicsSuiteAttributes,
    selectedNodeValues,
    selectedHierarchicalNodes,
    selectedGeophysicsAttributes,
    selectedImageCategory,
  } = useAppSelector((state) => state.images);

  // Get allImageTypes for hierarchical processing
  const allImageTypes = useAppSelector(selectAllImageTypes);

  // Get hierarchical nodes and parent-child relationships for debugging/usage
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const [geophysicsSuites, setGeophysicsSuites] = useState<IGeophysicsSuite[]>(
    []
  );
  const [maxResultCountDrillhole, setMaxResultCountDrillhole] = useState(100);
  const [keywordDrillhole, setKeywordDrillhole] = useState("");
  const { request: requestGetListImage } = useGetListImage();
  const { getCachedData, setCachedData, clearExpiredCache, clearAllCache } =
    useImageCache();
  const geophysicsLoading = useAppSelector(
    (state) => state.images.geophysicsLoading
  );
  const drillholeViewMode = useAppSelector(selectImages).drillholeViewMode;
  const projectDetail = useAppSelector((state) => state.project.detail);
  const projectDetailStatus = useAppSelector(
    (state) => state.project.getDetailStatus
  );

  // Geology panel UI control states
  const isTextWide = useAppSelector((state) => state.images.isTextWide);
  const showCombinedResultColumn = useAppSelector(
    (state) => state.images.showCombinedResultColumn
  );

  const dispatch = useAppDispatch();

  const handleScrollDrillhole = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountDrillhole(maxResultCountDrillhole + 100);
    }
  };

  const {
    data: listDrillHoles,
    loading: loadingListDrillHole,
    request: requestGetListDrillhole,
    setData: setListDrillHoles,
  } = useGetListDrillhole();

  const [isGetImages, setIsGetImages] = useState<RequestState>(
    RequestState.idle // Changed from initial to idle
  );

  // Add a function to fetch images with caching
  const fetchImagesWithCache = async (params: any, setLoadingFn?: Function) => {
    try {
      // Only add default StandardType if it's not already in params
      const paramsWithDefaultType = {
        ...params,
      };
      // Check cache first
      const cachedData = await getCachedData(paramsWithDefaultType);
      if (cachedData) {
        return cachedData;
      }

      // If no cache, make the API call
      const response = await requestGetListImage(
        paramsWithDefaultType,
        setLoadingFn
      );

      // Store the result in cache if successful
      if (response) {
        await setCachedData(paramsWithDefaultType, response);
      }

      return response;
    } catch (error) {
      // Fallback to direct API call if caching fails
      return requestGetListImage(params, setLoadingFn);
    }
  };

  // Clear expired cache entries when component mounts
  useEffect(() => {
    clearAllCache().catch(console.error);
  }, []);

  const onChangeGeology = async (_value: any, option: any) => {
    // Extract only serializable data from the option object to avoid circular references
    const serializableOption = option
      ? {
          label: option.label,
          value: option.value,
        }
      : null;

    // Update the selected geology suite state (single selection)
    dispatch(updateSelectedGeologySuite(serializableOption));

    // If a suite is selected, fetch geology suite details to get fields
    if (option) {
      try {
        const response = await geologySuiteRequest.getDetail(
          option.value.toString()
        );
        if (response.state === RequestState.success && response.data) {
          // Filter geology suite fields to show only Rock Group type fields
          const rockGroupFields = (
            response.data.geologySuiteFields || []
          ).filter(
            (field: any) => field.geologyField?.type === FieldType.RockGroup
          );
          dispatch(updateGeologySuiteFields(rockGroupFields));
        } else {
          dispatch(updateGeologySuiteFields([]));
        }
      } catch (error) {
        dispatch(updateGeologySuiteFields([]));
      }
    } else {
      // If no suite is selected (cleared), clear the fields and multi-logging data
      dispatch(updateGeologySuiteFields([]));
      dispatch(clearMultiLoggings());
      return;
    }

    if (selectedHoleNames.length === 0) return;

    // If a suite is selected, fetch geology data
    if (option) {
      dispatch(
        getMultiGeologyData({
          drillHoles: selectedHoleNames.map((name) => ({
            DrillholeName: name,
          })),
          suites: [
            {
              suiteName: option.label,
              suiteId: option.value,
            },
          ],
        })
      );
    }
  };

  const onChangeDrillholes = async (
    drillHoleNames: string[],
    options?: {
      hierarchicalNodes?: SelectedNodeList;
      imageCategory?: number | undefined;
    }
  ) => {
    dispatch(updateSelectedHoleNames(drillHoleNames));

    if (drillHoleNames.length === 0) {
      dispatch(updateImagesOfDrillHoles([]));
      return;
    }

    // Get geophysics data
    if (globalProjectId) {
      dispatch(
        getGeophysicsData({
          projectId: globalProjectId.toString(),
          pointsAttributes: geophysicsSuiteAttributes || [], // Use geophysicsSuiteAttributes
          selectedAttributes: selectedGeophysicsAttributes || [],
          selectedHoleNames,
        })
      );
    }

    // Refresh geology data if there is a selected geology suite
    if (selectedGeologySuite) {
      dispatch(
        getMultiGeologyData({
          drillHoles: drillHoleNames.map((name) => ({
            DrillholeName: name,
          })),
          suites: [
            {
              suiteName: selectedGeologySuite.label,
              suiteId: Number(selectedGeologySuite.value),
            },
          ],
        })
      );
    }

    let updatedImages: DrillHoleImages[] = [];
    const REQUEST_IMAGES_LIMIT = 100;

    // Phân loại các drill hole theo ImageTypeId and ImageSubtypeId

    for (const drillholeName of drillHoleNames) {
      for (const selectedNode of options?.hierarchicalNodes?.selectNodes ??
        []) {
        // Gọi api lấy ảnh của drill hole chưa có
        const totalImages: any[] = [];
        // Use the passed imageCategory value if provided, otherwise fall back to Redux state
        const categoryToUse =
          options?.imageCategory !== undefined
            ? options.imageCategory
            : selectedImageCategory;

        const queryParams: ImageQuery = {
          drillHoleNames: [drillholeName],
          skipCount: 0,
          maxResultCount: REQUEST_IMAGES_LIMIT,
          imageTypeId: selectedNode.typeId,
          imageSubtypeId: selectedNode.subTypeId,
          ...(categoryToUse && {
            imageCategory: categoryToUse,
          }),
          ignoreFields: [
            "segmentResult",
            "directOcrResult",
            "ocrResult",
            "segmentDetailResult",
            "boundingBox",
            "boundingRows",
            "rockLines",
          ],
        };

        // Use the cached version if available
        const listImages = await fetchImagesWithCache(queryParams);

        totalImages.push(...listImages?.result?.items);
        if (listImages?.result?.totalCount > REQUEST_IMAGES_LIMIT) {
          const totalRequest = Math.ceil(
            listImages?.result?.totalCount / REQUEST_IMAGES_LIMIT
          );

          for (let j = 1; j < totalRequest; j++) {
            const paginatedParams = {
              ...queryParams,
              skipCount: j * REQUEST_IMAGES_LIMIT,
            };

            // Use the cached version for pagination requests
            const paginatedImages = await fetchImagesWithCache(paginatedParams);
            totalImages.push(...paginatedImages?.result?.items);
          }
        }

        const findType = allImageTypes.find(
          (type) => type.id === selectedNode.typeId
        );
        const findSubType = findType?.imageSubtypes.find(
          (subtype) => Number(subtype.id) === selectedNode.subTypeId
        );

        updatedImages = [
          ...updatedImages,
          {
            list: totalImages,
            drillHole: totalImages[0]?.drillHole,
            type: selectedNode.typeId,
            subType: selectedNode.subTypeId,
            typeName: findType?.name,
            subTypeName: findSubType?.name,
          },
        ];
      }

      // Auto-switch to Original view when drillholes are selected and images are loaded
      // Commented out to prevent automatic view changes when selecting geology
      if (drillholeViewMode === EnumDrillholeView.NoChoice) {
        dispatch(updateDrillholeView(EnumDrillholeView.Original));
      }
      setIsGetImages(RequestState.success);

      // Remove empty images
      updatedImages = updatedImages.filter((image) => !isEmpty(image.list));
      dispatch(updateImagesOfDrillHoles(updatedImages));
    }
  };

  // Removed useGetListAttributeByProjectId and dataAttributes

  // Fetch geophysics suites when globalProjectId changes
  useEffect(() => {
    if (globalProjectId) {
      (async () => {
        const response = await suiteRequest.getSuiteByProjectId({
          Id: Number(globalProjectId),
        });
        if (response.state === RequestState.success && response.data) {
          setGeophysicsSuites(response.data);
        } else {
          setGeophysicsSuites([]);
        }
      })();
    } else {
      setGeophysicsSuites([]);
    }
  }, [globalProjectId]);

  // Track previous suite ID to detect actual changes
  const prevSuiteIdRef = useRef(selectedGeophysicsSuiteId);

  // Fetch attributes when selectedGeophysicsSuiteId changes
  useEffect(() => {
    const suiteIdChanged = prevSuiteIdRef.current !== selectedGeophysicsSuiteId;
    prevSuiteIdRef.current = selectedGeophysicsSuiteId;

    if (selectedGeophysicsSuiteId) {
      (async () => {
        const response = await suiteRequest.getAttributes({
          id: Number(selectedGeophysicsSuiteId),
        });
        if (response.state === RequestState.success && response.data) {
          dispatch(updateGeophysicsSuiteAttributes(response.data));
          // Only clear selections if the suite actually changed (not on component re-render)
          if (suiteIdChanged) {
            dispatch(updateSelectedGeophysicsAttributes([]));
          }
        } else {
          dispatch(updateGeophysicsSuiteAttributes([]));
          if (suiteIdChanged) {
            dispatch(updateSelectedGeophysicsAttributes([]));
          }
        }
      })();
    } else {
      dispatch(updateGeophysicsSuiteAttributes([]));
      if (suiteIdChanged) {
        dispatch(updateSelectedGeophysicsAttributes([]));
      }
    }
  }, [selectedGeophysicsSuiteId, dispatch]);

  useEffect(() => {
    dispatch(
      getRockGroups({
        projectId: String(globalProjectId),
      })
    );
  }, [globalProjectId]);

  // Clear field selection when geology suite fields change
  useEffect(() => {
    if ((geologySuiteFields || []).length === 0) {
      // Clear field selection when no fields are available
      dispatch(updateSelectedGeologyFieldId(undefined));
    }
  }, [geologySuiteFields, dispatch]);

  useEffect(() => {
    if (globalProjectId) {
      dispatch(getDetailProject(globalProjectId.toString()));
      // Reset default selection flag when project changes
    }
  }, [globalProjectId]);

  useEffectAfterMounted(() => {
    if (globalProjectId && globalProspectId) {
      requestGetListDrillhole({
        skipCount: 0,
        maxResultCount: 1000,
        projectIds: [globalProjectId],
        prospectIds: [globalProspectId],
        keyword: keywordDrillhole,
        sortField: "Name",
        sortOrder: SortOrder.ASC,
      });
    }
  }, [
    globalProjectId,
    globalProspectId,
    maxResultCountDrillhole,
    keywordDrillhole,
  ]);

  // Fetch drillholes from URL params
  useEffect(() => {
    (async () => {
      const drillHoleNames = searchParams.getAll("name") ?? [];
      if (listDrillHoles.length === 0 || drillHoleNames.length === 0) return;

      const notInListDrillHoles = drillHoleNames.filter(
        (item) => !listDrillHoles.find((drillhole) => drillhole.name === item)
      );
      for (let i = 0; i < notInListDrillHoles.length; i++) {
        const response = await drillHoleRequest.getListDrillHole({
          skipCount: 0,
          maxResultCount: 100,
          keyword: notInListDrillHoles[i],
          sortField: "Name",
          sortOrder: SortOrder.ASC,
        });
        if (response?.state === "success" && response?.data?.items.length > 0) {
          setListDrillHoles((prev) => [
            ...(response?.data?.items ?? []),
            ...prev,
          ]);
        }
      }
      if (
        !listDrillHoles.find((drillhole) =>
          drillHoleNames.includes(drillhole.name)
        )
      )
        return;

      onChangeDrillholes(drillHoleNames, {
        hierarchicalNodes: selectedHierarchicalNodes,
      });
    })();
  }, [
    JSON.stringify(searchParams.getAll("name")),
    JSON.stringify(listDrillHoles),
    JSON.stringify(selectedHierarchicalNodes),
    JSON.stringify(allImageTypes),
  ]);

  // Note: URL parameter handling for view, depth, and other filters is now managed by useUrlFilterSync hook
  const {
    data: imageTypeData,
    isLoading: isFetchingImageType,
    setSearchParams: setSearchParamsImageType,
    searchParams: imageTypeSearchParams,
  } = useQueryImageType(); // Assuming isLoading or isFetching is returned
  useEffect(() => {
    if (globalProjectId) {
      setSearchParamsImageType({
        ...imageTypeSearchParams,
        projectId: globalProjectId,
      });
    }
  }, [globalProjectId, setSearchParamsImageType]);
  // Populate allImageTypes when imageTypeData is loaded
  useEffect(() => {
    if (imageTypeData?.data?.items) {
      dispatch(setAllImageTypes(imageTypeData.data.items));
    }
  }, [imageTypeData, dispatch]);

  const transformDataToTreeNodes = (data: any[] | undefined) => {
    if (!data) return [];
    return data.map((type: any) => ({
      title: type.name,
      value: `type-${type.id}`,
      key: `type-${type.id}`,
      children: (type.imageSubtypes || []).map((subtype: any) => ({
        title: subtype.name,
        value: `subtype-${subtype.id}-type-${type.id}`,
        key: `subtype-${subtype.id}-type-${type.id}`,
      })),
    }));
  };

  const imageTypeTreeData = React.useMemo(
    () => transformDataToTreeNodes(imageTypeData?.data?.items),
    [imageTypeData]
  );

  const handleImageTypeTreeSelectChange = (selectedValues: string[]) => {
    console.log("handleImageTypeTreeSelectChange - selectedValues: ", selectedValues);
    // Update the flat selectedNodeValues for TreeSelect component
    dispatch(setSelectedNodeValues(selectedValues));
    // Transform to hierarchical structure and update hierarchical state
    const hierarchicalNodes = transformToHierarchicalNodes(
      selectedValues,
      imageTypeTreeData
    );
    console.log("handleImageTypeTreeSelectChange - hierarchicalNodes: ", hierarchicalNodes);

    dispatch(setSelectedHierarchicalNodes(hierarchicalNodes));

    onChangeDrillholes(selectedHoleNames, {
      hierarchicalNodes: hierarchicalNodes,
    });
  };

  const treeSelectValue = React.useMemo(() => {
    return selectedNodeValues || [];
  }, [selectedNodeValues]);
  console.log("selectedNodeValues: ", selectedNodeValues);

  // Handler for image category selection
  const handleImageCategoryChange = (value: number | undefined) => {
    dispatch(updateSelectedImageCategory(value));

    // Trigger image refresh with the new category filter
    // Pass the new value directly to avoid Redux state synchronization issues
    if (selectedHoleNames.length > 0) {
      onChangeDrillholes(selectedHoleNames, {
        hierarchicalNodes: selectedHierarchicalNodes,
        imageCategory: value, // Pass the new value directly
      });
    }
  };

  // Set default image category to "Drilling" (value: 1) if not already set
  useEffect(() => {
    if (selectedImageCategory === undefined) {
      dispatch(updateSelectedImageCategory(1)); // 1 = Drilling
    }
  }, [selectedImageCategory, dispatch]);

  // Simplified subtype selection function for standard image types
  const selectDefaultSubtypeForTree = (
    imageTypeId: number,
    subtypes: any[]
  ) => {
    if (!subtypes.length) {
      // If no subtypes available, just select the type without subtype
      const defaultTreeSelectValue = [`type-${imageTypeId}`];
      dispatch(setSelectedNodeValues(defaultTreeSelectValue));
      const hierarchicalNodes = transformToHierarchicalNodes(
        defaultTreeSelectValue,
        imageTypeTreeData
      );
      dispatch(setSelectedHierarchicalNodes(hierarchicalNodes));
      return;
    }

    // Find the first wet subtype (isWet === true)
    const wetSubtype = subtypes.find((subtype) => subtype.isWet === true);

    let selectedSubtypeId: number | undefined;

    // For standard image types, automatically select the first wet subtype if available
    if (wetSubtype) {
      selectedSubtypeId = Number(wetSubtype.id);
    }

    // Set the selected subtype and trigger image loading
    let defaultTreeSelectValue: string[];

    if (selectedSubtypeId) {
      defaultTreeSelectValue = [
        `subtype-${selectedSubtypeId}-type-${imageTypeId}`,
      ];
    } else {
      // No wet subtype found, just select the type
      defaultTreeSelectValue = [`type-${imageTypeId}`];
    }

    dispatch(setSelectedNodeValues(defaultTreeSelectValue));

    // Transform to hierarchical structure
    const hierarchicalNodes = transformToHierarchicalNodes(
      defaultTreeSelectValue,
      imageTypeTreeData
    );
    dispatch(setSelectedHierarchicalNodes(hierarchicalNodes));

    // Auto-trigger image loading if drillholes are selected
    if (selectedHoleNames.length > 0) {
      onChangeDrillholes(selectedHoleNames, {
        hierarchicalNodes: hierarchicalNodes,
      });
    }
  };

  // Simplified default selection logic for Image Types & Subtypes
  // useEffect(() => {
  //   // Ensure we have the necessary data and no current selection
  //   if (
  //     !allImageTypes ||
  //     allImageTypes.length === 0 ||
  //     selectedNodeValues.length > 0
  //   ) {
  //     return;
  //   }

  //   // Wait for project detail to be loaded before proceeding with default selection
  //   if (projectDetailStatus === RequestState.pending) {
  //     return;
  //   }

  //   let defaultImageTypeId: number | null = null;

  //   // Primary default: Use projectDetail.imageType.id if available

  //   if (projectDetail?.imageType?.id) {
  //     const projectImageTypeId = Number(projectDetail?.imageType?.id);

  //     // Verify this image type exists in allImageTypes
  //     const foundImageType = allImageTypes.find(
  //       (type) => type.id === projectImageTypeId
  //     );
  //     if (foundImageType) {
  //       defaultImageTypeId = projectImageTypeId;
  //     }
  //   }

  //   // Fallback default: Find first standard image type
  //   if (!defaultImageTypeId) {
  //     const standardImageType = allImageTypes.find(
  //       (type) => type.isStandard === true
  //     );
  //     if (standardImageType) {
  //       defaultImageTypeId = standardImageType.id;
  //     }
  //   }

  //   // Apply default selection if we found a valid image type
  //   if (defaultImageTypeId) {
  //     const defaultType = allImageTypes.find(
  //       (type) => type.id === defaultImageTypeId
  //     );

  //     if (defaultType) {
  //       // For standard types: automatically select first wet subtype
  //       if (defaultType.isStandard && defaultType.imageSubtypes?.length > 0) {
  //         selectDefaultSubtypeForTree(
  //           defaultImageTypeId,
  //           defaultType.imageSubtypes
  //         );
  //       } else {
  //         // For non-standard types: just select the type without subtype
  //         const defaultTreeSelectValue = [`type-${defaultImageTypeId}`];
  //         dispatch(setSelectedNodeValues(defaultTreeSelectValue));

  //         // Transform to hierarchical structure
  //         const hierarchicalNodes = transformToHierarchicalNodes(
  //           defaultTreeSelectValue,
  //           imageTypeTreeData
  //         );
  //         dispatch(setSelectedHierarchicalNodes(hierarchicalNodes));

  //         // Auto-trigger image loading if drillholes are selected
  //         if (selectedHoleNames.length > 0) {
  //           onChangeDrillholes(selectedHoleNames, {
  //             hierarchicalNodes: hierarchicalNodes,
  //           });
  //         }
  //       }
  //     }
  //   }
  // }, [
  //   projectDetail,
  //   projectDetailStatus,
  //   allImageTypes,
  //   selectedNodeValues,
  //   JSON.stringify(selectedHoleNames),
  // ]);

  return (
    <>
      <div
        className={` max-h-[calc(100vh-100px)] w-[260px]  pr-3 relative border-r border-gray-300 overflow-auto`}
      >
        <div className="mt-3">
          <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
            <div className="flex items-center">
              <span className="mr-1">
                {accountSettings?.collectionName
                  ? accountSettings?.collectionName
                  : "Drillhole"}
              </span>
              {isGetImages === RequestState.pending && ( // Changed from request to pending
                <span className="loading loading-spinner w-4"></span>
              )}
            </div>
          </div>
          <Select
            value={selectedHoleNames}
            onChange={(holeNames) => {
              // Update Redux state - URL will be automatically updated by useUrlFilterSync hook
              dispatch(updateSelectedHoleNames(holeNames));

              if (holeNames.length === 0) {
                dispatch(updateImagesOfDrillHoles([]));
                return;
              }

              // Call onChangeDrillholes to handle data fetching and geology refresh
              onChangeDrillholes(holeNames, {
                hierarchicalNodes: selectedHierarchicalNodes,
              });
            }}
            disabled={!globalProjectId}
            mode="multiple"
            onPopupScroll={handleScrollDrillhole}
            showSearch
            className="w-full h-full min-h-[38px]"
            placeholder="Select drillholes"
            notFoundContent={
              loadingListDrillHole ? (
                <Spin size="small" />
              ) : (
                <div className="text-center">No data</div>
              )
            }
            onSearch={(value) => {
              setKeywordDrillhole(value);
            }}
            searchValue={keywordDrillhole}
            onBlur={() => {
              setKeywordDrillhole("");
            }}
            allowClear
            filterOption={false}
            options={listDrillHoles.map((drillhole) => ({
              label: drillhole.name,
              value: drillhole.name,
            }))}
          />
        </div>

        {/* Image Category Selection */}
        <div className="mt-3">
          <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
            <div className="flex items-center">Image Category</div>
          </div>
          <Select
            value={selectedImageCategory}
            onChange={handleImageCategoryChange}
            placeholder="Select image category"
            className="w-full"
            allowClear
            options={imageCategoryOptions}
          />
        </div>

        <div className="mt-3">
          <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
            <div className="flex items-center">Image Types & Subtypes</div>
          </div>
          <TreeSelect
            treeData={imageTypeTreeData}
            value={treeSelectValue}
            onChange={handleImageTypeTreeSelectChange}
            multiple={true}
            treeCheckable={true}
            allowClear={true}
            placeholder="Select image types and/or subtypes"
            style={{ width: "100%" }}
            showCheckedStrategy={TreeSelect.SHOW_PARENT}
            loading={isFetchingImageType}
            className="w-full h-full"
            treeDefaultExpandAll // Optional: to show all nodes expanded by default
          />
        </div>

        {/* Geology Section - Only show in DrillHole view */}
        {alignValue === ImageView.DrillHole && (
          <>
            <div className="mt-3">
              <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
                <div className="flex items-center">
                  <span className="mr-1">Geology</span>
                </div>
              </div>
              <Select
                className="w-full h-full"
                placeholder="Choose suite"
                value={selectedGeologySuite?.value}
                options={(projectDetail?.geologySuites ?? []).map(
                  (attribute: any) => ({
                    label: attribute.name,
                    value: attribute.id,
                  })
                )}
                onChange={(value, option) => onChangeGeology(value, option)}
                allowClear
              />
            </div>

            <div className="flex gap-2 items-center my-2">
              <p>Field</p>
              <Select
                options={(geologySuiteFields || []).map((field) => ({
                  label: field.geologyField?.name || field.name,
                  value: field.id, // Use geology suite field ID
                }))}
                value={selectedGeologyFieldId}
                placeholder="Select field"
                className="w-full"
                onChange={(value) => {
                  dispatch(updateSelectedGeologyFieldId(value));
                }}
                onClear={() => {
                  dispatch(updateSelectedGeologyFieldId(undefined));
                }}
                allowClear
                disabled={(geologySuiteFields || []).length === 0}
                notFoundContent={
                  (geologySuiteFields || []).length === 0 ? (
                    <div className="text-center">
                      {selectedGeologySuite
                        ? "No Rock Group fields available"
                        : "Please select a geology suite first"}
                    </div>
                  ) : null
                }
              />
            </div>
          </>
        )}

        {/* Geology Panel UI Controls - Only show in DrillHole view */}
        {alignValue === ImageView.DrillHole && (
          <div className="mt-3 space-y-3">
            {/* Combined Logging Visibility */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Show Log</span>
              <Checkbox
                checked={showCombinedResultColumn}
                onChange={(e) => {
                  dispatch(
                    updateCombinedResultColumnVisibility(e.target.checked)
                  );
                }}
              />
            </div>

            {/* Text Width Toggle */}
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Text Width</span>
              <div className="flex items-center gap-2">
                <span className="text-xs">Narrow</span>
                <Switch
                  checked={isTextWide}
                  onChange={(checked) => {
                    dispatch(updateTextWidth(checked));
                  }}
                  size="small"
                />
                <span className="text-xs">Wide</span>
              </div>
            </div>
          </div>
        )}

        {/* Geophysics Suite Selection - Only show in DrillHole view */}
        {alignValue === ImageView.DrillHole && (
          <div className="mt-3">
            <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
              <div className="flex items-center">
                <span className="mr-1">Geophysics Suite</span>
              </div>
            </div>
            <Select
              className="w-full h-full"
              placeholder="Select geophysics suite"
              value={selectedGeophysicsSuiteId}
              onChange={(value) => {
                dispatch(updateSelectedGeophysicsSuiteId(value));
              }}
              allowClear
              showSearch
              optionFilterProp="children"
              filterOption={(input, option: any) =>
                (option?.label ?? "")
                  .toLowerCase()
                  .includes(input.toLowerCase())
              }
              options={geophysicsSuites.map((suite) => ({
                label: suite.name,
                value: suite.id,
              }))}
              disabled={!globalProjectId || geophysicsSuites.length === 0}
              notFoundContent={
                geophysicsSuites.length === 0 ? (
                  <div className="text-center">No suites available</div>
                ) : null
              }
            />
          </div>
        )}

        {/* Geophysics Attribute Selection - Only show in DrillHole view */}
        {alignValue === ImageView.DrillHole &&
          selectedGeophysicsSuiteId &&
          (geophysicsSuiteAttributes || []).length > 0 && (
            <div className="mt-3">
              <div className="font-medium mb-2 flex justify-between items-center bg-primary px-2 py-1 text-white">
                <div className="flex items-center">
                  <span className="mr-1">Geophysics Attributes</span>
                  {geophysicsLoading && (
                    <span className="loading loading-spinner w-4"></span>
                  )}
                </div>
              </div>
              <Checkbox.Group
                value={selectedGeophysicsAttributes}
                style={{ width: "100%" }}
                onChange={(currentSelectedAttributes) => {
                  dispatch(
                    updateSelectedGeophysicsAttributes(
                      currentSelectedAttributes
                    )
                  );
                  if (globalProjectId) {
                    dispatch(
                      getGeophysicsData({
                        projectId: globalProjectId.toString(),
                        pointsAttributes: geophysicsSuiteAttributes || [], // Use geophysicsSuiteAttributes
                        selectedAttributes: currentSelectedAttributes,
                        selectedHoleNames,
                      })
                    );
                  }
                }}
              >
                <Row>
                  {(geophysicsSuiteAttributes || []).map(
                    (attribute: ISuiteAttribute) => {
                      return (
                        <Col key={attribute.id} span={24} className="mb-2">
                          <Checkbox value={attribute.name}>
                            {" "}
                            {/* Assuming attribute.name is the value to store */}
                            {attribute.name}
                          </Checkbox>
                        </Col>
                      );
                    }
                  )}
                </Row>
              </Checkbox.Group>
            </div>
          )}
        {alignValue === ImageView.DrillHole &&
          selectedGeophysicsSuiteId &&
          (geophysicsSuiteAttributes || []).length === 0 &&
          !geophysicsLoading && (
            <div className="mt-3 text-center">
              No attributes found for the selected suite.
            </div>
          )}
      </div>
    </>
  );
}

export default React.memo(DrillHolePanel);
