// Simple test file to verify tree value conversion functions
import {
  hierarchicalNodesToTreeValues,
  treeValuesToHierarchicalNodes,
  serializeTreeValues,
  deserializeTreeValues,
} from "./url-params.util";
import { SelectedNodeList } from "../interface/image.interface";

// Test data
const testHierarchicalNodes: SelectedNodeList = {
  selectNodes: [
    { id: "type-1", typeId: 1 }, // Type only
    { id: "subtype-2-type-3", typeId: 3, subTypeId: 2 }, // Subtype
    { id: "subtype-4-type-5", typeId: 5, subTypeId: 4 }, // Another subtype
  ],
};

const expectedTreeValues = [
  "type-1",
  "subtype-2-type-3", 
  "subtype-4-type-5"
];

// Test conversion functions
console.log("=== Testing Tree Value Conversion Functions ===");

// Test hierarchicalNodesToTreeValues
console.log("\n1. Testing hierarchicalNodesToTreeValues:");
console.log("Input:", testHierarchicalNodes);
const convertedTreeValues = hierarchicalNodesToTreeValues(testHierarchicalNodes);
console.log("Output:", convertedTreeValues);
console.log("Expected:", expectedTreeValues);
console.log("Match:", JSON.stringify(convertedTreeValues) === JSON.stringify(expectedTreeValues));

// Test treeValuesToHierarchicalNodes
console.log("\n2. Testing treeValuesToHierarchicalNodes:");
console.log("Input:", expectedTreeValues);
const convertedHierarchical = treeValuesToHierarchicalNodes(expectedTreeValues);
console.log("Output:", convertedHierarchical);
console.log("Expected:", testHierarchicalNodes);

// Check if the conversion is reversible
const backToTreeValues = hierarchicalNodesToTreeValues(convertedHierarchical);
console.log("Reversible:", JSON.stringify(backToTreeValues) === JSON.stringify(expectedTreeValues));

// Test serialization/deserialization
console.log("\n3. Testing serialization/deserialization:");
const serialized = serializeTreeValues(expectedTreeValues);
console.log("Serialized:", serialized);
const deserialized = deserializeTreeValues(serialized);
console.log("Deserialized:", deserialized);
console.log("Serialization reversible:", JSON.stringify(deserialized) === JSON.stringify(expectedTreeValues));

console.log("\n=== Test Complete ===");
